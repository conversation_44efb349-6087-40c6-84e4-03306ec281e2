import { Controller, Get, Post, Inject, Query } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { DataConsistencyService } from '../service/data-consistency.service';
import { CustomError } from '../error/custom.error';

@Controller('/admin/data-consistency')
export class DataConsistencyController {
  @Inject()
  ctx: Context;

  @Inject()
  dataConsistencyService: DataConsistencyService;

  @Get('/check', { summary: '检查数据一致性' })
  async checkConsistency() {
    return await this.dataConsistencyService.checkDataConsistency();
  }

  @Get('/stats', { summary: '获取冗余字段统计信息' })
  async getStats() {
    return await this.dataConsistencyService.getRedundantFieldStats();
  }

  @Post('/repair', { summary: '修复数据不一致' })
  async repairInconsistency(@Query('method') method = 'auto') {
    if (method === 'batch') {
      // 使用批量修复方法（适用于大数据量）
      return await this.dataConsistencyService.batchRepairInconsistency();
    } else {
      // 使用自动修复方法（默认）
      return await this.dataConsistencyService.autoRepairInconsistency();
    }
  }

  @Post('/repair-missing', { summary: '修复缺失的冗余字段' })
  async repairMissingFields() {
    return await this.dataConsistencyService.repairServiceRedundantFields();
  }

  @Post('/maintenance', { summary: '执行完整维护任务' })
  async performMaintenance() {
    return await this.dataConsistencyService.performMaintenanceTasks();
  }

  @Get('/validate', { summary: '验证修复结果' })
  async validateRepair() {
    return await this.dataConsistencyService.validateRepairResult();
  }

  @Post('/sync-service', { summary: '同步特定服务信息到订单详情' })
  async syncServiceToOrderDetails(
    @Query('serviceId') serviceId: number,
    @Query('serviceName') serviceName?: string,
    @Query('basePrice') basePrice?: number
  ) {
    if (!serviceId) {
      throw new CustomError('服务ID不能为空');
    }

    const updates: any = {};
    if (serviceName) updates.serviceName = serviceName;
    if (basePrice !== undefined)
      updates.basePrice = parseFloat(basePrice.toString());

    if (Object.keys(updates).length === 0) {
      throw new CustomError('至少需要提供服务名称或价格中的一个');
    }

    return await this.dataConsistencyService.syncServiceToOrderDetails(
      serviceId,
      updates
    );
  }

  @Get('/check-original-price', { summary: '检查缺失原价的订单' })
  async checkMissingOriginalPrice() {
    return await this.dataConsistencyService.checkMissingOriginalPrice();
  }

  @Get('/order-price-stats', { summary: '获取订单原价统计信息' })
  async getOrderPriceStats() {
    return await this.dataConsistencyService.getOrderPriceStats();
  }

  @Post('/repair-original-price', { summary: '补充订单原价' })
  async repairOriginalPrice(@Query('method') method = 'auto') {
    if (method === 'batch') {
      // 使用批量补充方法（适用于大数据量）
      return await this.dataConsistencyService.batchRepairOrderOriginalPrice();
    } else {
      // 使用逐条补充方法（默认）
      return await this.dataConsistencyService.repairOrderOriginalPrice();
    }
  }

  @Get('/check-abnormal-price', { summary: '检查异常价格订单' })
  async checkAbnormalPrice(
    @Query('threshold') threshold?: number,
    @Query('limit') limit?: number
  ) {
    return await this.dataConsistencyService.checkAbnormalPriceOrders({
      threshold: threshold ? parseFloat(threshold.toString()) : undefined,
      limit: limit ? parseInt(limit.toString()) : undefined,
    });
  }

  @Get('/abnormal-price-detail', { summary: '获取异常价格详情' })
  async getAbnormalPriceDetail(@Query('orderId') orderId: number) {
    if (!orderId) {
      throw new CustomError('订单ID不能为空');
    }
    return await this.dataConsistencyService.getAbnormalPriceDetail(parseInt(orderId.toString()));
  }

  @Post('/batch-fix-abnormal-price', { summary: '批量修正异常价格' })
  async batchFixAbnormalPrice(
    @Query('orderIds') orderIds: string,
    @Query('fixType') fixType: 'recalculate' | 'adjust' = 'recalculate'
  ) {
    if (!orderIds) {
      throw new CustomError('订单ID列表不能为空');
    }

    const ids = orderIds
      .split(',')
      .map(id => parseInt(id.trim()))
      .filter(id => !isNaN(id));
    if (ids.length === 0) {
      throw new CustomError('无效的订单ID列表');
    }

    return await this.dataConsistencyService.batchFixAbnormalPrices(
      ids,
      fixType
    );
  }

  @Post('/set-abnormal-threshold', { summary: '设置异常价格阈值' })
  async setAbnormalThreshold(@Query('threshold') threshold: number) {
    if (!threshold || threshold < 0 || threshold > 100) {
      throw new CustomError('阈值必须在0-100之间');
    }
    return await this.dataConsistencyService.setAbnormalPriceThreshold(
      threshold
    );
  }

  @Get('/abnormal-price-report', { summary: '获取异常价格统计报告' })
  async getAbnormalPriceReport(@Query('threshold') threshold?: number) {
    return await this.dataConsistencyService.getAbnormalPriceReport(
      threshold ? parseFloat(threshold.toString()) : undefined
    );
  }
}
