-- 补充订单原价数据迁移脚本
-- 用于修复历史订单中缺失的原价信息

-- 1. 检查缺失原价的订单数量
SELECT 
    COUNT(*) as total_missing_orders,
    COUNT(CASE WHEN originalPrice = 0 THEN 1 END) as zero_price_orders,
    COUNT(CASE WHEN originalPrice IS NULL THEN 1 END) as null_price_orders
FROM orders 
WHERE originalPrice = 0 OR originalPrice IS NULL;

-- 2. 查看缺失原价的订单详情（前10条）
SELECT 
    o.id,
    o.sn,
    o.originalPrice,
    o.totalFee,
    o.cardDeduction,
    o.couponDeduction,
    o.status,
    o.orderTime
FROM orders o
WHERE o.originalPrice = 0 OR o.originalPrice IS NULL
ORDER BY o.orderTime DESC
LIMIT 10;

-- 3. 检查订单详情中的价格信息完整性
SELECT 
    COUNT(*) as total_order_details,
    COUNT(CASE WHEN od.servicePrice > 0 THEN 1 END) as has_service_price,
    COUNT(CASE WHEN s.basePrice > 0 THEN 1 END) as has_base_price,
    COUNT(CASE WHEN od.servicePrice > 0 OR s.basePrice > 0 THEN 1 END) as has_any_price
FROM order_details od
LEFT JOIN services s ON od.serviceId = s.id
INNER JOIN orders o ON od.orderId = o.id
WHERE o.originalPrice = 0 OR o.originalPrice IS NULL;

-- 4. 批量更新订单原价（使用冗余字段优先）
UPDATE orders o
SET originalPrice = (
    SELECT COALESCE(SUM(
        CASE 
            WHEN od.servicePrice > 0 THEN od.servicePrice
            ELSE COALESCE(s.basePrice, 0)
        END
    ), 0)
    FROM order_details od
    LEFT JOIN services s ON od.serviceId = s.id
    WHERE od.orderId = o.id
)
WHERE o.originalPrice = 0 OR o.originalPrice IS NULL;

-- 5. 验证修复结果
SELECT 
    COUNT(*) as total_orders,
    COUNT(CASE WHEN originalPrice > 0 THEN 1 END) as valid_price_orders,
    COUNT(CASE WHEN originalPrice = 0 OR originalPrice IS NULL THEN 1 END) as still_missing_orders,
    ROUND(COUNT(CASE WHEN originalPrice > 0 THEN 1 END) * 100.0 / COUNT(*), 2) as fill_rate_percent
FROM orders;

-- 6. 检查价格异常的订单（原价小于实付金额）
SELECT 
    o.id,
    o.sn,
    o.originalPrice,
    o.totalFee,
    o.cardDeduction,
    o.couponDeduction,
    (o.originalPrice - o.totalFee) as price_difference
FROM orders o
WHERE o.originalPrice > 0 
  AND o.originalPrice < o.totalFee
ORDER BY price_difference ASC
LIMIT 20;

-- 7. 统计各状态订单的原价完整性
SELECT 
    o.status,
    COUNT(*) as total_count,
    COUNT(CASE WHEN o.originalPrice > 0 THEN 1 END) as valid_price_count,
    COUNT(CASE WHEN o.originalPrice = 0 OR o.originalPrice IS NULL THEN 1 END) as missing_price_count,
    ROUND(COUNT(CASE WHEN o.originalPrice > 0 THEN 1 END) * 100.0 / COUNT(*), 2) as fill_rate_percent
FROM orders o
GROUP BY o.status
ORDER BY missing_price_count DESC;

-- 8. 查看修复后的订单原价分布
SELECT 
    CASE 
        WHEN originalPrice = 0 THEN '0元'
        WHEN originalPrice > 0 AND originalPrice <= 50 THEN '1-50元'
        WHEN originalPrice > 50 AND originalPrice <= 100 THEN '51-100元'
        WHEN originalPrice > 100 AND originalPrice <= 200 THEN '101-200元'
        WHEN originalPrice > 200 AND originalPrice <= 500 THEN '201-500元'
        WHEN originalPrice > 500 THEN '500元以上'
        ELSE '异常'
    END as price_range,
    COUNT(*) as order_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM orders), 2) as percentage
FROM orders
GROUP BY 
    CASE 
        WHEN originalPrice = 0 THEN '0元'
        WHEN originalPrice > 0 AND originalPrice <= 50 THEN '1-50元'
        WHEN originalPrice > 50 AND originalPrice <= 100 THEN '51-100元'
        WHEN originalPrice > 100 AND originalPrice <= 200 THEN '101-200元'
        WHEN originalPrice > 200 AND originalPrice <= 500 THEN '201-500元'
        WHEN originalPrice > 500 THEN '500元以上'
        ELSE '异常'
    END
ORDER BY 
    CASE 
        WHEN price_range = '0元' THEN 1
        WHEN price_range = '1-50元' THEN 2
        WHEN price_range = '51-100元' THEN 3
        WHEN price_range = '101-200元' THEN 4
        WHEN price_range = '201-500元' THEN 5
        WHEN price_range = '500元以上' THEN 6
        ELSE 7
    END;

-- 9. 创建索引优化查询性能（如果不存在）
CREATE INDEX IF NOT EXISTS idx_orders_original_price ON orders(originalPrice);
CREATE INDEX IF NOT EXISTS idx_orders_status_price ON orders(status, originalPrice);

-- 10. 备份修复前的数据（可选）
-- CREATE TABLE orders_backup_before_price_repair AS 
-- SELECT * FROM orders WHERE originalPrice = 0 OR originalPrice IS NULL;

-- 使用说明：
-- 1. 执行前请先备份数据库
-- 2. 建议先在测试环境执行验证
-- 3. 可以分批执行，避免长时间锁表
-- 4. 执行后检查验证结果，确保数据正确性
