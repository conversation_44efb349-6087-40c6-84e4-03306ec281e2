-- 为订单详情表添加服务冗余字段
-- 这些字段用于减少查询时的嵌套层级，提升性能

-- 添加服务名称冗余字段
ALTER TABLE order_details 
ADD COLUMN serviceName VARCHAR(100) NOT NULL DEFAULT '' 
COMMENT '服务名称，确保删除服务后订单明细的服务名称不丢失';

-- 添加服务价格冗余字段
ALTER TABLE order_details 
ADD COLUMN servicePrice DECIMAL(8,2) NOT NULL DEFAULT 0.00 
COMMENT '服务基础价格，确保服务价格变更后订单明细的价格不受影响';

-- 为新字段添加索引（可选，根据查询需求决定）
CREATE INDEX idx_order_details_service_name ON order_details(serviceName);
CREATE INDEX idx_order_details_service_price ON order_details(servicePrice);

-- 数据迁移：将现有订单详情的服务信息填充到冗余字段
UPDATE order_details od
INNER JOIN services s ON od.serviceId = s.id
SET 
    od.serviceName = s.serviceName,
    od.servicePrice = s.basePrice
WHERE od.serviceName = '' OR od.servicePrice = 0.00;

-- 验证数据迁移结果
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN serviceName != '' THEN 1 END) as filled_service_names,
    COUNT(CASE WHEN servicePrice > 0 THEN 1 END) as filled_service_prices
FROM order_details;

-- 查看迁移后的示例数据
SELECT 
    od.id,
    od.serviceId,
    od.serviceName,
    od.servicePrice,
    s.serviceName as current_service_name,
    s.basePrice as current_service_price
FROM order_details od
LEFT JOIN services s ON od.serviceId = s.id
LIMIT 10;
