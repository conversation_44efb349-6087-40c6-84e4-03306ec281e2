-- 创建异常价格检测相关表

-- 1. 创建服务价格历史记录表
CREATE TABLE IF NOT EXISTS service_price_histories (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '历史记录ID',
  serviceId INT NOT NULL COMMENT '关联服务ID',
  oldPrice DECIMAL(8,2) NOT NULL COMMENT '变更前价格',
  newPrice DECIMAL(8,2) NOT NULL COMMENT '变更后价格',
  changeReason VARCHAR(100) NOT NULL COMMENT '变更原因',
  changedBy VARCHAR(50) NOT NULL COMMENT '变更操作人',
  changeTime DATETIME NOT NULL COMMENT '变更时间',
  effectiveTime DATETIME NOT NULL COMMENT '生效时间',
  remark TEXT COMMENT '备注',
  createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_service_id (serviceId),
  INDEX idx_change_time (changeTime),
  INDEX idx_effective_time (effectiveTime),
  
  FOREIGN KEY (serviceId) REFERENCES services(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务价格历史记录表';

-- 2. 创建异常价格配置表
CREATE TABLE IF NOT EXISTS abnormal_price_configs (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
  configKey VARCHAR(50) NOT NULL UNIQUE COMMENT '配置键',
  configValue VARCHAR(200) NOT NULL COMMENT '配置值',
  description VARCHAR(200) NOT NULL COMMENT '配置描述',
  configType ENUM('threshold', 'rule', 'setting') NOT NULL COMMENT '配置类型',
  isEnabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
  createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_config_key (configKey),
  INDEX idx_config_type (configType),
  INDEX idx_is_enabled (isEnabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='异常价格配置表';

-- 3. 插入默认配置数据
INSERT INTO abnormal_price_configs (configKey, configValue, description, configType, isEnabled) VALUES
('abnormal_threshold', '10', '异常价格检测阈值（百分比）', 'threshold', TRUE),
('max_discount_rate', '50', '最大折扣率（百分比）', 'threshold', TRUE),
('min_order_amount', '10', '最小订单金额', 'threshold', TRUE),
('enable_auto_fix', 'false', '是否启用自动修复', 'setting', TRUE),
('notification_enabled', 'true', '是否启用异常通知', 'setting', TRUE),
('check_frequency', '24', '检查频率（小时）', 'setting', TRUE);

-- 4. 创建异常价格检测的索引优化
CREATE INDEX IF NOT EXISTS idx_orders_price_analysis ON orders(originalPrice, totalFee, cardDeduction, couponDeduction);
CREATE INDEX IF NOT EXISTS idx_orders_status_price ON orders(status, originalPrice);

-- 5. 创建视图：异常价格订单视图
CREATE OR REPLACE VIEW v_abnormal_price_orders AS
SELECT 
    o.id,
    o.sn,
    o.originalPrice,
    o.totalFee,
    o.cardDeduction,
    o.couponDeduction,
    o.status,
    o.orderTime,
    ABS(o.originalPrice - o.totalFee) as priceDifference,
    CASE 
        WHEN o.originalPrice > 0 THEN 
            ABS(o.originalPrice - o.totalFee) / o.originalPrice * 100
        ELSE 0 
    END as differenceRate,
    CASE 
        WHEN o.cardDeduction > 0 OR o.couponDeduction > 0 THEN '有优惠抵扣'
        WHEN o.originalPrice > o.totalFee THEN '价格偏低'
        WHEN o.originalPrice < o.totalFee THEN '价格偏高'
        ELSE '价格正常'
    END as priceStatus
FROM orders o
WHERE o.originalPrice > 0 
  AND o.totalFee > 0
  AND ABS(o.originalPrice - o.totalFee) / o.originalPrice * 100 > 5;

-- 6. 创建存储过程：批量检查异常价格
DELIMITER //
CREATE PROCEDURE CheckAbnormalPrices(IN threshold_percent DECIMAL(5,2))
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE order_id INT;
    DECLARE order_sn VARCHAR(50);
    DECLARE original_price DECIMAL(8,2);
    DECLARE total_fee DECIMAL(8,2);
    DECLARE difference_rate DECIMAL(5,2);
    
    DECLARE cur CURSOR FOR 
        SELECT id, sn, originalPrice, totalFee,
               ABS(originalPrice - totalFee) / originalPrice * 100 as diff_rate
        FROM orders 
        WHERE originalPrice > 0 
          AND totalFee > 0
          AND ABS(originalPrice - totalFee) / originalPrice * 100 > threshold_percent
        ORDER BY diff_rate DESC;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 创建临时结果表
    DROP TEMPORARY TABLE IF EXISTS temp_abnormal_orders;
    CREATE TEMPORARY TABLE temp_abnormal_orders (
        orderId INT,
        orderSn VARCHAR(50),
        originalPrice DECIMAL(8,2),
        totalFee DECIMAL(8,2),
        differenceRate DECIMAL(5,2),
        checkTime DATETIME DEFAULT CURRENT_TIMESTAMP
    );
    
    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO order_id, order_sn, original_price, total_fee, difference_rate;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        INSERT INTO temp_abnormal_orders (orderId, orderSn, originalPrice, totalFee, differenceRate)
        VALUES (order_id, order_sn, original_price, total_fee, difference_rate);
    END LOOP;
    CLOSE cur;
    
    -- 返回结果
    SELECT * FROM temp_abnormal_orders ORDER BY differenceRate DESC;
END //
DELIMITER ;

-- 7. 创建触发器：记录服务价格变更历史
DELIMITER //
CREATE TRIGGER tr_service_price_history 
AFTER UPDATE ON services
FOR EACH ROW
BEGIN
    IF OLD.basePrice != NEW.basePrice THEN
        INSERT INTO service_price_histories (
            serviceId, oldPrice, newPrice, changeReason, changedBy, 
            changeTime, effectiveTime, remark
        ) VALUES (
            NEW.id, OLD.basePrice, NEW.basePrice, '价格更新', 'system',
            NOW(), NOW(), CONCAT('价格从', OLD.basePrice, '更新为', NEW.basePrice)
        );
    END IF;
END //
DELIMITER ;

-- 8. 验证表创建
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('service_price_histories', 'abnormal_price_configs');

-- 9. 验证配置数据
SELECT * FROM abnormal_price_configs ORDER BY configType, configKey;
