import { Provide, Inject, ILogger } from '@midwayjs/core';
import { Service, OrderDetail, Order } from '../entity';
import { Op, literal } from 'sequelize';

/**
 * 数据一致性维护服务
 * 负责维护冗余字段与主表数据的一致性
 */
@Provide()
export class DataConsistencyService {
  @Inject()
  logger: ILogger;

  /**
   * 同步服务信息到订单详情
   * 当服务名称或价格发生变更时调用
   */
  async syncServiceToOrderDetails(
    serviceId: number,
    updates: {
      serviceName?: string;
      basePrice?: number;
    }
  ) {
    try {
      const updateData: any = {};

      if (updates.serviceName) {
        updateData.serviceName = updates.serviceName;
      }

      if (updates.basePrice !== undefined) {
        updateData.servicePrice = updates.basePrice;
      }

      if (Object.keys(updateData).length > 0) {
        const [affectedCount] = await OrderDetail.update(updateData, {
          where: { serviceId },
        });

        this.logger.info(
          `同步服务信息到订单详情完成，影响记录数：${affectedCount}`
        );
        return affectedCount;
      }

      return 0;
    } catch (error) {
      this.logger.error('同步服务信息到订单详情失败:', error);
      throw error;
    }
  }

  /**
   * 批量修复订单详情中的服务冗余字段
   * 用于数据修复或初始化
   */
  async repairServiceRedundantFields() {
    try {
      // 查找缺失服务名称或价格的订单详情
      const brokenDetails = await OrderDetail.findAll({
        where: {
          [Op.or]: [
            { serviceName: '' },
            { serviceName: null },
            { servicePrice: 0 },
            { servicePrice: null },
          ],
        },
        include: [Service],
        limit: 1000, // 分批处理，避免内存溢出
      });

      let repairedCount = 0;

      for (const detail of brokenDetails) {
        if (detail.service) {
          await detail.update({
            serviceName: detail.service.serviceName,
            servicePrice: detail.service.basePrice,
          });
          repairedCount++;
        }
      }

      this.logger.info(`修复服务冗余字段完成，修复记录数：${repairedCount}`);
      return repairedCount;
    } catch (error) {
      this.logger.error('修复服务冗余字段失败:', error);
      throw error;
    }
  }

  /**
   * 检查数据一致性
   * 返回不一致的记录数量和详情
   */
  async checkDataConsistency() {
    try {
      // 使用 Sequelize 查询不一致的数据
      const inconsistentRecords = await OrderDetail.findAll({
        attributes: [
          'id',
          'serviceId',
          ['serviceName', 'detail_service_name'],
          ['servicePrice', 'detail_service_price'],
        ],
        include: [
          {
            model: Service,
            attributes: [
              ['serviceName', 'actual_service_name'],
              ['basePrice', 'actual_service_price'],
            ],
            required: true,
          },
        ],
        where: {
          [Op.or]: [
            literal('`OrderDetail`.`serviceName` != `service`.`serviceName`'),
            literal('`OrderDetail`.`servicePrice` != `service`.`basePrice`'),
          ],
        },
        limit: 100,
        raw: true,
        nest: true,
      });

      const inconsistentCount = inconsistentRecords.length;

      this.logger.info(
        `数据一致性检查完成，发现不一致记录：${inconsistentCount}条`
      );

      return {
        inconsistentCount,
        inconsistentRecords,
        isConsistent: inconsistentCount === 0,
      };
    } catch (error) {
      this.logger.error('数据一致性检查失败:', error);
      throw error;
    }
  }

  /**
   * 自动修复数据不一致问题
   */
  async autoRepairInconsistency() {
    try {
      const checkResult = await this.checkDataConsistency();

      if (checkResult.isConsistent) {
        this.logger.info('数据一致性检查通过，无需修复');
        return { repairedCount: 0, message: '数据一致性正常' };
      }

      // 获取需要修复的记录
      const recordsToRepair = await OrderDetail.findAll({
        include: [
          {
            model: Service,
            attributes: ['serviceName', 'basePrice'],
            required: true,
          },
        ],
        where: {
          [Op.or]: [
            literal('`OrderDetail`.`serviceName` != `service`.`serviceName`'),
            literal('`OrderDetail`.`servicePrice` != `service`.`basePrice`'),
          ],
        },
      });

      let repairedCount = 0;

      // 批量修复数据 - 使用事务提高性能
      const transaction = await OrderDetail.sequelize.transaction();

      try {
        for (const record of recordsToRepair) {
          if (record.service) {
            await record.update(
              {
                serviceName: record.service.serviceName,
                servicePrice: record.service.basePrice,
              },
              { transaction }
            );
            repairedCount++;
          }
        }

        await transaction.commit();
      } catch (error) {
        await transaction.rollback();
        throw error;
      }

      this.logger.info(`自动修复数据不一致完成，修复记录数：${repairedCount}`);

      return {
        repairedCount,
        message: `成功修复${repairedCount}条不一致记录`,
      };
    } catch (error) {
      this.logger.error('自动修复数据不一致失败:', error);
      throw error;
    }
  }

  /**
   * 获取冗余字段统计信息
   */
  async getRedundantFieldStats() {
    try {
      // 使用 Sequelize 聚合查询
      const totalRecords = await OrderDetail.count();

      const filledServiceNames = await OrderDetail.count({
        where: {
          serviceName: {
            [Op.and]: [{ [Op.ne]: null }, { [Op.ne]: '' }],
          },
        },
      });

      const filledServicePrices = await OrderDetail.count({
        where: {
          servicePrice: {
            [Op.and]: [{ [Op.ne]: null }, { [Op.gt]: 0 }],
          },
        },
      });

      const emptyServiceNames = await OrderDetail.count({
        where: {
          [Op.or]: [{ serviceName: null }, { serviceName: '' }],
        },
      });

      const emptyServicePrices = await OrderDetail.count({
        where: {
          [Op.or]: [{ servicePrice: null }, { servicePrice: 0 }],
        },
      });

      return {
        totalRecords,
        filledServiceNames,
        filledServicePrices,
        emptyServiceNames,
        emptyServicePrices,
        serviceNameFillRate:
          totalRecords > 0
            ? ((filledServiceNames / totalRecords) * 100).toFixed(2) + '%'
            : '0%',
        servicePriceFillRate:
          totalRecords > 0
            ? ((filledServicePrices / totalRecords) * 100).toFixed(2) + '%'
            : '0%',
      };
    } catch (error) {
      this.logger.error('获取冗余字段统计信息失败:', error);
      throw error;
    }
  }

  /**
   * 定期维护任务
   * 建议在低峰期执行
   */
  async performMaintenanceTasks() {
    try {
      this.logger.info('开始执行数据一致性维护任务');

      // 1. 检查数据一致性
      const consistencyResult = await this.checkDataConsistency();

      // 2. 如果发现不一致，自动修复
      let repairResult = null;
      if (!consistencyResult.isConsistent) {
        repairResult = await this.autoRepairInconsistency();
      }

      // 3. 修复缺失的冗余字段
      const repairCount = await this.repairServiceRedundantFields();

      // 4. 获取统计信息
      const stats = await this.getRedundantFieldStats();

      const maintenanceResult = {
        timestamp: new Date(),
        consistencyCheck: consistencyResult,
        repairResult,
        repairMissingFields: repairCount,
        statistics: stats,
      };

      this.logger.info('数据一致性维护任务完成', maintenanceResult);

      return maintenanceResult;
    } catch (error) {
      this.logger.error('数据一致性维护任务失败:', error);
      throw error;
    }
  }

  /**
   * 高效批量修复方法
   * 使用原生SQL进行批量更新，适用于大数据量场景
   */
  async batchRepairInconsistency() {
    try {
      this.logger.info('开始执行批量修复数据不一致');

      // 对于大数据量，使用原生SQL更高效
      const sequelize = OrderDetail.sequelize;
      const [result] = await sequelize.query(`
        UPDATE order_details od
        INNER JOIN services s ON od.serviceId = s.id
        SET
          od.serviceName = s.serviceName,
          od.servicePrice = s.basePrice
        WHERE od.serviceName != s.serviceName
           OR od.servicePrice != s.basePrice
      `);

      const repairedCount = (result as any).affectedRows || 0;

      this.logger.info(`批量修复数据不一致完成，修复记录数：${repairedCount}`);

      return {
        repairedCount,
        message: `批量修复成功，共修复${repairedCount}条记录`,
      };
    } catch (error) {
      this.logger.error('批量修复数据不一致失败:', error);
      throw error;
    }
  }

  /**
   * 验证修复结果
   */
  async validateRepairResult() {
    try {
      const beforeStats = await this.getRedundantFieldStats();
      const consistencyCheck = await this.checkDataConsistency();

      return {
        isValid: consistencyCheck.isConsistent,
        statistics: beforeStats,
        inconsistentCount: consistencyCheck.inconsistentCount,
        message: consistencyCheck.isConsistent
          ? '数据一致性验证通过'
          : `仍有${consistencyCheck.inconsistentCount}条记录不一致`,
      };
    } catch (error) {
      this.logger.error('验证修复结果失败:', error);
      throw error;
    }
  }

  /**
   * 检查缺失原价的订单
   * 返回需要补充原价的订单数量和详情
   */
  async checkMissingOriginalPrice() {
    try {
      // 查找原价为0或null的订单
      const missingPriceOrders = await Order.findAll({
        where: {
          [Op.or]: [{ originalPrice: 0 }, { originalPrice: null }],
        },
        attributes: [
          'id',
          'sn',
          'originalPrice',
          'totalFee',
          'cardDeduction',
          'couponDeduction',
        ],
        include: [
          {
            model: OrderDetail,
            attributes: ['id', 'serviceId', 'serviceName', 'servicePrice'],
            include: [
              {
                model: Service,
                attributes: ['id', 'serviceName', 'basePrice'],
                required: false,
              },
            ],
          },
        ],
        limit: 100,
      });

      const missingCount = missingPriceOrders.length;

      this.logger.info(
        `检查缺失原价订单完成，发现${missingCount}个订单需要补充原价`
      );

      return {
        missingCount,
        missingOrders: missingPriceOrders,
        needsRepair: missingCount > 0,
      };
    } catch (error) {
      this.logger.error('检查缺失原价订单失败:', error);
      throw error;
    }
  }

  /**
   * 计算订单原价
   * 基于订单详情中的服务价格计算
   */
  private calculateOrderOriginalPrice(orderDetails: OrderDetail[]): number {
    let totalPrice = 0;

    for (const detail of orderDetails) {
      // 优先使用冗余字段中的价格
      if (detail.servicePrice && detail.servicePrice > 0) {
        totalPrice += detail.servicePrice;
      }
      // 如果冗余字段没有价格，使用关联服务的价格
      else if (detail.service && detail.service.basePrice) {
        totalPrice += detail.service.basePrice;
      }
      // 如果都没有，记录警告但继续处理
      else {
        this.logger.warn(`订单详情 ${detail.id} 缺少价格信息`);
      }
    }

    return totalPrice;
  }

  /**
   * 补充订单原价
   * 根据订单详情中的服务价格计算并更新原价
   */
  async repairOrderOriginalPrice() {
    try {
      const checkResult = await this.checkMissingOriginalPrice();

      if (!checkResult.needsRepair) {
        this.logger.info('所有订单原价完整，无需补充');
        return { repairedCount: 0, message: '所有订单原价完整' };
      }

      let repairedCount = 0;
      const transaction = await Order.sequelize.transaction();

      try {
        for (const order of checkResult.missingOrders) {
          if (order.orderDetails && order.orderDetails.length > 0) {
            // 计算原价
            const originalPrice = this.calculateOrderOriginalPrice(
              order.orderDetails
            );

            if (originalPrice > 0) {
              await order.update(
                {
                  originalPrice,
                },
                { transaction }
              );

              repairedCount++;

              this.logger.info(
                `订单 ${order.sn} 原价已补充：${originalPrice}元`
              );
            }
          }
        }

        await transaction.commit();

        this.logger.info(`补充订单原价完成，共处理${repairedCount}个订单`);

        return {
          repairedCount,
          message: `成功补充${repairedCount}个订单的原价`,
        };
      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    } catch (error) {
      this.logger.error('补充订单原价失败:', error);
      throw error;
    }
  }

  /**
   * 批量补充订单原价
   * 使用原生SQL进行批量更新，适用于大数据量场景
   */
  async batchRepairOrderOriginalPrice() {
    try {
      this.logger.info('开始批量补充订单原价');

      // 使用原生SQL批量更新
      const sequelize = Order.sequelize;
      const [result] = await sequelize.query(`
        UPDATE orders o
        SET originalPrice = (
          SELECT COALESCE(SUM(
            CASE
              WHEN od.servicePrice > 0 THEN od.servicePrice
              ELSE s.basePrice
            END
          ), 0)
          FROM order_details od
          LEFT JOIN services s ON od.serviceId = s.id
          WHERE od.orderId = o.id
        )
        WHERE o.originalPrice = 0 OR o.originalPrice IS NULL
      `);

      const repairedCount = (result as any).affectedRows || 0;

      this.logger.info(`批量补充订单原价完成，共处理${repairedCount}个订单`);

      return {
        repairedCount,
        message: `批量补充成功，共处理${repairedCount}个订单的原价`,
      };
    } catch (error) {
      this.logger.error('批量补充订单原价失败:', error);
      throw error;
    }
  }

  /**
   * 获取订单原价统计信息
   */
  async getOrderPriceStats() {
    try {
      const totalOrders = await Order.count();

      const missingOriginalPrice = await Order.count({
        where: {
          [Op.or]: [{ originalPrice: 0 }, { originalPrice: null }],
        },
      });

      const validOriginalPrice = await Order.count({
        where: {
          originalPrice: {
            [Op.gt]: 0,
          },
        },
      });

      // 计算价格异常的订单（原价小于实付金额）
      const abnormalPriceOrders = await Order.count({
        where: {
          [Op.and]: [
            { originalPrice: { [Op.gt]: 0 } },
            literal('`originalPrice` < `totalFee`'),
          ],
        },
      });

      return {
        totalOrders,
        missingOriginalPrice,
        validOriginalPrice,
        abnormalPriceOrders,
        originalPriceFillRate:
          totalOrders > 0
            ? ((validOriginalPrice / totalOrders) * 100).toFixed(2) + '%'
            : '0%',
        abnormalPriceRate:
          totalOrders > 0
            ? ((abnormalPriceOrders / totalOrders) * 100).toFixed(2) + '%'
            : '0%',
      };
    } catch (error) {
      this.logger.error('获取订单原价统计信息失败:', error);
      throw error;
    }
  }

  /**
   * 检查异常价格订单
   * 根据配置的阈值检测价格异常的订单
   */
  async checkAbnormalPriceOrders(options: {
    threshold?: number;  // 异常阈值百分比，默认10%
    limit?: number;      // 返回记录数限制
  } = {}) {
    try {
      const { threshold = 10, limit = 100 } = options;

      // 查找价格异常的订单
      const abnormalOrders = await Order.findAll({
        where: {
          [Op.and]: [
            { originalPrice: { [Op.gt]: 0 } },
            { totalFee: { [Op.gt]: 0 } },
            // 价格差异超过阈值
            literal(`ABS(\`originalPrice\` - \`totalFee\`) / \`originalPrice\` * 100 > ${threshold}`)
          ]
        },
        attributes: [
          'id', 'sn', 'originalPrice', 'totalFee',
          'cardDeduction', 'couponDeduction', 'status', 'orderTime'
        ],
        include: [
          {
            model: OrderDetail,
            attributes: ['id', 'serviceId', 'serviceName', 'servicePrice'],
            include: [
              {
                model: Service,
                attributes: ['id', 'serviceName', 'basePrice'],
                required: false
              }
            ]
          }
        ],
        limit,
        order: [
          [literal('ABS(`originalPrice` - `totalFee`) / `originalPrice`'), 'DESC']
        ]
      });

      // 分析异常原因
      const analyzedOrders = abnormalOrders.map(order => {
        const priceDifference = order.originalPrice - order.totalFee;
        const differenceRate = Math.abs(priceDifference) / order.originalPrice * 100;

        // 分析异常原因
        const abnormalReason = this.analyzeAbnormalReason(order, priceDifference);

        return {
          ...order.toJSON(),
          priceDifference: parseFloat(priceDifference.toFixed(2)),
          differenceRate: parseFloat(differenceRate.toFixed(2)),
          abnormalReason,
          calculatedPrice: this.calculateOrderPrice(order.orderDetails),
          isConfirmedAbnormal: this.isConfirmedAbnormal(order, abnormalReason)
        };
      });

      return {
        abnormalCount: abnormalOrders.length,
        threshold,
        abnormalOrders: analyzedOrders
      };
    } catch (error) {
      this.logger.error('检查异常价格订单失败:', error);
      throw error;
    }
  }

  /**
   * 分析异常原因
   */
  private analyzeAbnormalReason(order: Order, priceDifference: number): string[] {
    const reasons: string[] = [];

    // 权益卡抵扣
    if (order.cardDeduction > 0) {
      reasons.push(`权益卡抵扣: ${order.cardDeduction}元`);
    }

    // 代金券抵扣
    if (order.couponDeduction > 0) {
      reasons.push(`代金券抵扣: ${order.couponDeduction}元`);
    }

    // 计算预期抵扣金额
    const expectedDeduction = order.cardDeduction + order.couponDeduction;
    const actualDifference = Math.abs(priceDifference);

    // 判断是否为正常的优惠抵扣
    if (Math.abs(actualDifference - expectedDeduction) <= 0.01) {
      reasons.push('正常优惠抵扣');
    } else if (actualDifference > expectedDeduction) {
      const extraDiscount = actualDifference - expectedDeduction;
      reasons.push(`额外折扣: ${extraDiscount.toFixed(2)}元`);
    } else {
      reasons.push('抵扣金额不匹配');
    }

    // 检查是否为促销活动
    if (priceDifference > 0 && order.cardDeduction === 0 && order.couponDeduction === 0) {
      reasons.push('可能的促销活动');
    }

    return reasons;
  }

  /**
   * 计算订单应有价格
   */
  private calculateOrderPrice(orderDetails: OrderDetail[]): number {
    let totalPrice = 0;

    for (const detail of orderDetails) {
      if (detail.servicePrice && detail.servicePrice > 0) {
        totalPrice += detail.servicePrice;
      } else if (detail.service && detail.service.basePrice) {
        totalPrice += detail.service.basePrice;
      }
    }

    return totalPrice;
  }

  /**
   * 判断是否为确认的异常
   */
  private isConfirmedAbnormal(_order: Order, reasons: string[]): boolean {
    // 如果包含"正常优惠抵扣"，则不是异常
    if (reasons.includes('正常优惠抵扣')) {
      return false;
    }

    // 如果包含"抵扣金额不匹配"或"额外折扣"，则可能是异常
    if (reasons.some(reason =>
      reason.includes('抵扣金额不匹配') ||
      reason.includes('额外折扣')
    )) {
      return true;
    }

    return false;
  }

  /**
   * 批量修正异常价格
   */
  async batchFixAbnormalPrices(orderIds: number[], fixType: 'recalculate' | 'adjust' = 'recalculate') {
    try {
      let fixedCount = 0;
      const transaction = await Order.sequelize.transaction();

      try {
        for (const orderId of orderIds) {
          const order = await Order.findByPk(orderId, {
            include: [
              {
                model: OrderDetail,
                include: [Service]
              }
            ],
            transaction
          });

          if (!order) continue;

          let newOriginalPrice: number;

          if (fixType === 'recalculate') {
            // 重新计算原价
            newOriginalPrice = this.calculateOrderPrice(order.orderDetails);
          } else {
            // 根据实付金额调整原价
            newOriginalPrice = order.totalFee + order.cardDeduction + order.couponDeduction;
          }

          if (newOriginalPrice !== order.originalPrice) {
            await order.update({ originalPrice: newOriginalPrice }, { transaction });
            fixedCount++;

            this.logger.info(`订单 ${order.sn} 原价已修正：${order.originalPrice} -> ${newOriginalPrice}`);
          }
        }

        await transaction.commit();

        this.logger.info(`批量修正异常价格完成，共处理${fixedCount}个订单`);

        return {
          fixedCount,
          message: `成功修正${fixedCount}个订单的异常价格`
        };
      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    } catch (error) {
      this.logger.error('批量修正异常价格失败:', error);
      throw error;
    }
  }

  /**
   * 获取异常价格详情
   */
  async getAbnormalPriceDetail(orderId: number) {
    try {
      const order = await Order.findByPk(orderId, {
        include: [
          {
            model: OrderDetail,
            include: [
              {
                model: Service,
                attributes: ['id', 'serviceName', 'basePrice']
              }
            ]
          }
        ]
      });

      if (!order) {
        throw new Error('订单不存在');
      }

      const calculatedPrice = this.calculateOrderPrice(order.orderDetails);
      const priceDifference = order.originalPrice - order.totalFee;
      const differenceRate = Math.abs(priceDifference) / order.originalPrice * 100;
      const abnormalReason = this.analyzeAbnormalReason(order, priceDifference);

      return {
        order: {
          id: order.id,
          sn: order.sn,
          originalPrice: order.originalPrice,
          totalFee: order.totalFee,
          cardDeduction: order.cardDeduction,
          couponDeduction: order.couponDeduction,
          status: order.status,
          orderTime: order.orderTime
        },
        priceAnalysis: {
          calculatedPrice,
          priceDifference: parseFloat(priceDifference.toFixed(2)),
          differenceRate: parseFloat(differenceRate.toFixed(2)),
          abnormalReason,
          isConfirmedAbnormal: this.isConfirmedAbnormal(order, abnormalReason)
        },
        orderDetails: order.orderDetails.map(detail => ({
          id: detail.id,
          serviceId: detail.serviceId,
          serviceName: detail.serviceName,
          servicePrice: detail.servicePrice,
          currentServicePrice: detail.service?.basePrice || 0,
          priceDifference: (detail.servicePrice || 0) - (detail.service?.basePrice || 0)
        }))
      };
    } catch (error) {
      this.logger.error('获取异常价格详情失败:', error);
      throw error;
    }
  }

  /**
   * 配置异常价格阈值
   */
  async setAbnormalPriceThreshold(threshold: number) {
    try {
      // 这里可以将阈值保存到配置表或缓存中
      // 暂时返回设置结果
      if (threshold < 0 || threshold > 100) {
        throw new Error('阈值必须在0-100之间');
      }

      this.logger.info(`异常价格阈值已设置为：${threshold}%`);

      return {
        threshold,
        message: `异常价格阈值已设置为${threshold}%`
      };
    } catch (error) {
      this.logger.error('设置异常价格阈值失败:', error);
      throw error;
    }
  }

  /**
   * 获取异常价格统计报告
   */
  async getAbnormalPriceReport(threshold: number = 10) {
    try {
      const abnormalResult = await this.checkAbnormalPriceOrders({ threshold, limit: 1000 });

      // 按异常原因分类统计
      const reasonStats = new Map<string, number>();
      let confirmedAbnormalCount = 0;

      abnormalResult.abnormalOrders.forEach(order => {
        if (order.isConfirmedAbnormal) {
          confirmedAbnormalCount++;
        }

        order.abnormalReason.forEach(reason => {
          const count = reasonStats.get(reason) || 0;
          reasonStats.set(reason, count + 1);
        });
      });

      return {
        threshold,
        totalAbnormalCount: abnormalResult.abnormalCount,
        confirmedAbnormalCount,
        suspiciousCount: abnormalResult.abnormalCount - confirmedAbnormalCount,
        reasonStatistics: Array.from(reasonStats.entries()).map(([reason, count]) => ({
          reason,
          count,
          percentage: ((count / abnormalResult.abnormalCount) * 100).toFixed(2) + '%'
        })),
        topAbnormalOrders: abnormalResult.abnormalOrders.slice(0, 10)
      };
    } catch (error) {
      this.logger.error('获取异常价格统计报告失败:', error);
      throw error;
    }
  }
}
