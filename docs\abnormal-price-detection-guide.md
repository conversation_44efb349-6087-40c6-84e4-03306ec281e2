# 异常价格检测功能使用指南

## 功能概述

异常价格检测功能是数据一致性维护服务的重要组成部分，旨在自动识别和处理订单中的价格异常情况，确保订单价格的准确性和合理性。

## 核心功能

### 1. 异常价格阈值配置
- **功能**：允许管理员配置异常价格的判断阈值
- **默认阈值**：10%（价格差异超过原价的10%视为异常）
- **可调范围**：0-100%
- **配置方式**：通过 API 接口动态配置

### 2. 异常原因智能分析
系统会自动分析价格异常的可能原因：

#### 正常情况
- **权益卡抵扣**：使用权益卡享受折扣
- **代金券抵扣**：使用代金券减免费用
- **正常优惠抵扣**：抵扣金额与价格差异匹配

#### 异常情况
- **抵扣金额不匹配**：实际价格差异与抵扣金额不符
- **额外折扣**：存在未记录的额外折扣
- **可能的促销活动**：无抵扣记录但价格偏低

### 3. 异常价格详情分析
提供订单的详细价格分析：
- **原价 vs 实付金额**：对比分析
- **计算价格**：基于服务明细重新计算的价格
- **差异金额**：具体的价格差异数值
- **差异率**：价格差异的百分比
- **异常确认**：系统判断是否为真正的异常

### 4. 批量处理功能
对确认需要修正的异常价格提供批量处理：

#### 修复方式
- **重新计算**：基于订单详情重新计算原价
- **调整原价**：根据实付金额和抵扣调整原价

#### 处理流程
1. 检测异常订单
2. 分析异常原因
3. 确认需要修复的订单
4. 批量执行修复操作
5. 验证修复结果

### 5. 历史价格记录
保留服务价格的历史变更记录：
- **价格变更历史**：记录每次价格调整
- **变更原因**：记录价格变更的原因
- **操作人员**：记录执行变更的人员
- **生效时间**：记录价格生效的时间

## 使用场景

### 1. 日常价格监控
```javascript
// 每日检查异常价格
const dailyCheck = async () => {
  const result = await fetch('/admin/data-consistency/check-abnormal-price?threshold=10');
  const data = await result.json();
  
  if (data.errCode === 0 && data.data.abnormalCount > 0) {
    console.log(`发现 ${data.data.abnormalCount} 个异常价格订单`);
    // 发送告警通知
  }
};
```

### 2. 异常价格分析
```javascript
// 获取详细的异常价格报告
const getAbnormalReport = async () => {
  const result = await fetch('/admin/data-consistency/abnormal-price-report?threshold=15');
  const data = await result.json();
  
  if (data.errCode === 0) {
    const report = data.data;
    console.log(`总异常数：${report.totalAbnormalCount}`);
    console.log(`确认异常：${report.confirmedAbnormalCount}`);
    console.log(`可疑订单：${report.suspiciousCount}`);
  }
};
```

### 3. 批量修复异常
```javascript
// 批量修复确认的异常价格
const batchFix = async (orderIds) => {
  const result = await fetch(
    `/admin/data-consistency/batch-fix-abnormal-price?orderIds=${orderIds.join(',')}&fixType=recalculate`,
    { method: 'POST' }
  );
  
  const data = await result.json();
  if (data.errCode === 0) {
    console.log(`成功修复 ${data.data.fixedCount} 个订单`);
  }
};
```

## 配置管理

### 默认配置项
| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| abnormal_threshold | 10 | 异常价格检测阈值（%） |
| max_discount_rate | 50 | 最大折扣率（%） |
| min_order_amount | 10 | 最小订单金额 |
| enable_auto_fix | false | 是否启用自动修复 |
| notification_enabled | true | 是否启用异常通知 |
| check_frequency | 24 | 检查频率（小时） |

### 配置修改
```javascript
// 设置异常价格阈值
const setThreshold = async (threshold) => {
  const result = await fetch(
    `/admin/data-consistency/set-abnormal-threshold?threshold=${threshold}`,
    { method: 'POST' }
  );
  
  const data = await result.json();
  if (data.errCode === 0) {
    console.log(`阈值已设置为 ${threshold}%`);
  }
};
```

## 数据库设计

### 服务价格历史表 (service_price_histories)
```sql
CREATE TABLE service_price_histories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  serviceId INT NOT NULL,
  oldPrice DECIMAL(8,2) NOT NULL,
  newPrice DECIMAL(8,2) NOT NULL,
  changeReason VARCHAR(100) NOT NULL,
  changedBy VARCHAR(50) NOT NULL,
  changeTime DATETIME NOT NULL,
  effectiveTime DATETIME NOT NULL,
  remark TEXT,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL
);
```

### 异常价格配置表 (abnormal_price_configs)
```sql
CREATE TABLE abnormal_price_configs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  configKey VARCHAR(50) NOT NULL UNIQUE,
  configValue VARCHAR(200) NOT NULL,
  description VARCHAR(200) NOT NULL,
  configType ENUM('threshold', 'rule', 'setting') NOT NULL,
  isEnabled BOOLEAN NOT NULL DEFAULT TRUE,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL
);
```

## 最佳实践

### 1. 阈值设置建议
- **保守设置**：初始阈值设为 5-10%，避免误报
- **业务调整**：根据业务特点调整阈值
- **分类设置**：不同服务类型可设置不同阈值

### 2. 检查频率
- **实时检查**：订单创建时实时检查
- **定期检查**：每日凌晨批量检查
- **手动检查**：异常情况下手动触发

### 3. 处理流程
1. **自动检测**：系统自动识别异常订单
2. **人工审核**：管理员审核异常原因
3. **批量处理**：确认后批量修复
4. **结果验证**：验证修复效果

### 4. 监控告警
```javascript
// 设置监控告警
const monitorAbnormalPrices = async () => {
  const report = await getAbnormalReport();
  
  // 异常率超过5%时告警
  const abnormalRate = report.confirmedAbnormalCount / report.totalAbnormalCount;
  if (abnormalRate > 0.05) {
    await sendAlert(`异常价格率过高：${(abnormalRate * 100).toFixed(2)}%`);
  }
};
```

## 故障排除

### 常见问题

1. **误报过多**
   - 检查阈值设置是否过低
   - 分析业务场景是否正常
   - 调整异常判断逻辑

2. **漏检异常**
   - 检查阈值设置是否过高
   - 验证数据完整性
   - 检查计算逻辑

3. **修复失败**
   - 检查数据权限
   - 验证订单状态
   - 查看错误日志

### 数据验证
```sql
-- 验证修复效果
SELECT 
  COUNT(*) as total_orders,
  COUNT(CASE WHEN ABS(originalPrice - totalFee) / originalPrice * 100 > 10 THEN 1 END) as abnormal_count,
  AVG(ABS(originalPrice - totalFee) / originalPrice * 100) as avg_difference_rate
FROM orders 
WHERE originalPrice > 0 AND totalFee > 0;
```

## 总结

异常价格检测功能通过智能分析、自动检测、批量处理等方式，有效保障了订单价格的准确性。合理配置和使用该功能，可以显著提升数据质量和业务准确性。
