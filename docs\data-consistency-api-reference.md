# 数据一致性维护服务 API 接口文档

## 概述

数据一致性维护服务提供了一套完整的 API 接口，用于管理和维护系统中的数据一致性，包括服务冗余字段维护和订单原价补充功能。

**基础路径：** `/admin/data-consistency`

**认证要求：** 需要管理员权限

**返回格式说明：** 所有接口都遵循项目统一返回格式 `{ errCode, msg, data }`，成功时 errCode 为 0，失败时由统一异常处理机制处理。

## 📋 接口列表

### 1. 服务冗余字段相关接口

#### 1.1 检查数据一致性
```http
GET /admin/data-consistency/check
```

**功能描述：** 检查订单详情中服务冗余字段与主表数据的一致性

**请求参数：** 无

**成功响应：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "inconsistentCount": 5,
    "inconsistentRecords": [
      {
        "id": 123,
        "serviceId": 1,
        "detail_service_name": "旧服务名",
        "actual_service_name": "新服务名",
        "detail_service_price": 100.00,
        "actual_service_price": 120.00
      }
    ],
    "isConsistent": false
  }
}
```

#### 1.2 获取冗余字段统计信息
```http
GET /admin/data-consistency/stats
```

**功能描述：** 获取服务冗余字段的填充率和完整性统计

**请求参数：** 无

**成功响应：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "totalRecords": 1000,
    "filledServiceNames": 950,
    "filledServicePrices": 945,
    "emptyServiceNames": 50,
    "emptyServicePrices": 55,
    "serviceNameFillRate": "95.00%",
    "servicePriceFillRate": "94.50%"
  }
}
```

#### 1.3 修复数据不一致
```http
POST /admin/data-consistency/repair?method=auto
```

**功能描述：** 修复订单详情中与服务主表不一致的冗余字段数据

**请求参数：**
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| method | string | 否 | auto | 修复方法：`auto`(逐条修复) 或 `batch`(批量修复) |

**成功响应：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "repairedCount": 5,
    "message": "成功修复5条不一致记录"
  }
}
```

#### 1.4 修复缺失的冗余字段
```http
POST /admin/data-consistency/repair-missing
```

**功能描述：** 修复订单详情中缺失的服务名称和价格冗余字段

**请求参数：** 无

**成功响应：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": 25
}
```

#### 1.5 同步特定服务信息
```http
POST /admin/data-consistency/sync-service?serviceId=1&serviceName=新服务名&basePrice=150.00
```

**功能描述：** 将特定服务的最新信息同步到所有相关的订单详情记录

**请求参数：**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| serviceId | number | 是 | 服务ID |
| serviceName | string | 否 | 新的服务名称 |
| basePrice | number | 否 | 新的服务价格 |

**成功响应：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": 15
}
```
```

### 2. 订单原价相关接口

#### 2.1 检查缺失原价的订单
```http
GET /admin/data-consistency/check-original-price
```

**功能描述：** 检查系统中原价为0或null的订单

**请求参数：** 无

**成功响应：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "missingCount": 150,
    "missingOrders": [
      {
        "id": 1001,
        "sn": "***********",
        "originalPrice": 0,
        "totalFee": 120.00,
        "cardDeduction": 20.00,
        "couponDeduction": 0,
        "orderDetails": [...]
      }
    ],
    "needsRepair": true
  }
}
```

#### 2.2 获取订单原价统计信息
```http
GET /admin/data-consistency/order-price-stats
```

**功能描述：** 获取订单原价数据的完整性统计

**请求参数：** 无

**成功响应：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "totalOrders": 1000,
    "missingOriginalPrice": 150,
    "validOriginalPrice": 850,
    "abnormalPriceOrders": 5,
    "originalPriceFillRate": "85.00%",
    "abnormalPriceRate": "0.50%"
  }
}
```

#### 2.3 补充订单原价
```http
POST /admin/data-consistency/repair-original-price?method=batch
```

**功能描述：** 为缺失原价的订单自动计算并补充原价信息

**请求参数：**
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| method | string | 否 | auto | 修复方法：`auto`(逐条修复) 或 `batch`(批量修复，推荐) |

**成功响应：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "repairedCount": 150,
    "message": "成功补充150个订单的原价"
  }
}
```

### 3. 异常价格检测相关接口

#### 3.1 检查异常价格订单
```http
GET /admin/data-consistency/check-abnormal-price?threshold=10&limit=100
```

**功能描述：** 根据配置的阈值检测价格异常的订单

**请求参数：**
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| threshold | number | 否 | 10 | 异常阈值百分比 |
| limit | number | 否 | 100 | 返回记录数限制 |

**成功响应：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "abnormalCount": 25,
    "threshold": 10,
    "abnormalOrders": [
      {
        "id": 1001,
        "sn": "***********",
        "originalPrice": 200.00,
        "totalFee": 120.00,
        "cardDeduction": 50.00,
        "couponDeduction": 0,
        "priceDifference": 80.00,
        "differenceRate": 40.00,
        "abnormalReason": ["权益卡抵扣: 50.00元", "额外折扣: 30.00元"],
        "calculatedPrice": 200.00,
        "isConfirmedAbnormal": true
      }
    ]
  }
}
```

#### 3.2 获取异常价格详情
```http
GET /admin/data-consistency/abnormal-price-detail/1001
```

**功能描述：** 获取指定订单的异常价格详细分析

**请求参数：**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| orderId | number | 是 | 订单ID |

**成功响应：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "order": {
      "id": 1001,
      "sn": "***********",
      "originalPrice": 200.00,
      "totalFee": 120.00,
      "cardDeduction": 50.00,
      "couponDeduction": 0,
      "status": "已完成",
      "orderTime": "2024-12-01T10:30:00.000Z"
    },
    "priceAnalysis": {
      "calculatedPrice": 200.00,
      "priceDifference": 80.00,
      "differenceRate": 40.00,
      "abnormalReason": ["权益卡抵扣: 50.00元", "额外折扣: 30.00元"],
      "isConfirmedAbnormal": true
    },
    "orderDetails": [
      {
        "id": 2001,
        "serviceId": 1,
        "serviceName": "宠物洗护",
        "servicePrice": 100.00,
        "currentServicePrice": 100.00,
        "priceDifference": 0
      }
    ]
  }
}
```

#### 3.3 批量修正异常价格
```http
POST /admin/data-consistency/batch-fix-abnormal-price?orderIds=1001,1002,1003&fixType=recalculate
```

**功能描述：** 批量修正确认异常的订单价格

**请求参数：**
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| orderIds | string | 是 | - | 订单ID列表，逗号分隔 |
| fixType | string | 否 | recalculate | 修复类型：`recalculate`(重新计算) 或 `adjust`(调整) |

**成功响应：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "fixedCount": 3,
    "message": "成功修正3个订单的异常价格"
  }
}
```

#### 3.4 设置异常价格阈值
```http
POST /admin/data-consistency/set-abnormal-threshold?threshold=15
```

**功能描述：** 配置异常价格检测的阈值

**请求参数：**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| threshold | number | 是 | 阈值百分比（0-100） |

**成功响应：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "threshold": 15,
    "message": "异常价格阈值已设置为15%"
  }
}
```

#### 3.5 获取异常价格统计报告
```http
GET /admin/data-consistency/abnormal-price-report?threshold=10
```

**功能描述：** 获取异常价格的统计分析报告

**请求参数：**
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| threshold | number | 否 | 10 | 异常阈值百分比 |

**成功响应：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "threshold": 10,
    "totalAbnormalCount": 50,
    "confirmedAbnormalCount": 15,
    "suspiciousCount": 35,
    "reasonStatistics": [
      {
        "reason": "权益卡抵扣: 50.00元",
        "count": 20,
        "percentage": "40.00%"
      },
      {
        "reason": "正常优惠抵扣",
        "count": 25,
        "percentage": "50.00%"
      }
    ],
    "topAbnormalOrders": [...]
  }
}
```

### 4. 综合维护接口

#### 4.1 执行完整维护任务
```http
POST /admin/data-consistency/maintenance
```

**功能描述：** 执行完整的数据一致性维护任务，包括检查、修复、统计等

**请求参数：** 无

**成功响应：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "timestamp": "2024-12-01T10:30:00.000Z",
    "consistencyCheck": {
      "inconsistentCount": 5,
      "isConsistent": false
    },
    "repairResult": {
      "repairedCount": 5,
      "message": "成功修复5条不一致记录"
    },
    "repairMissingFields": 25,
    "statistics": {
      "totalRecords": 1000,
      "serviceNameFillRate": "98.50%",
      "servicePriceFillRate": "97.80%"
    }
  }
}
```

#### 4.2 验证修复结果
```http
GET /admin/data-consistency/validate
```

**功能描述：** 验证数据修复操作的结果

**请求参数：** 无

**成功响应：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "isValid": true,
    "statistics": {
      "totalRecords": 1000,
      "serviceNameFillRate": "100.00%",
      "servicePriceFillRate": "100.00%"
    },
    "inconsistentCount": 0,
    "message": "数据一致性验证通过"
  }
}
```

## 🚨 错误响应格式

所有接口在出现错误时都会返回统一的错误格式（由项目统一异常处理机制处理）：

**业务错误响应：**
```json
{
  "errCode": 400,
  "msg": "服务ID不能为空"
}
```

**系统错误响应：**
```json
{
  "errCode": 500,
  "msg": "系统内部错误"
}
```

**常见错误码：**
- `0`: 成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `404`: 资源不存在
- `422`: 参数校验错误
- `500`: 系统内部错误

## 📊 使用建议

### 1. 推荐的操作流程
```
1. 获取统计信息 → 了解数据状况
2. 检查数据一致性 → 发现问题
3. 执行修复操作 → 解决问题
4. 验证修复结果 → 确认效果
```

### 2. 性能考虑
- **小数据量（< 1000条）**：使用 `method=auto`
- **大数据量（> 1000条）**：使用 `method=batch`
- **建议在低峰期执行维护任务**

### 3. 监控建议
- 定期调用统计接口监控数据质量
- 设置告警阈值（如填充率 < 95%）
- 建立定期维护计划

## 🔐 权限要求

所有接口都需要管理员权限，请确保：
1. 用户已登录且具有管理员角色
2. 请求头中包含有效的认证信息
3. 具有数据维护相关权限

## � 前端调用示例

### JavaScript/TypeScript 调用示例

```javascript
// 1. 检查数据一致性
async function checkDataConsistency() {
  try {
    const response = await fetch('/admin/data-consistency/check');
    const result = await response.json();

    if (result.errCode === 0) {
      const { inconsistentCount, isConsistent } = result.data;
      if (!isConsistent) {
        console.log(`发现 ${inconsistentCount} 条不一致记录`);
      }
    } else {
      console.error('检查失败:', result.msg);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
}

// 2. 获取统计信息
async function getStats() {
  try {
    const response = await fetch('/admin/data-consistency/stats');
    const result = await response.json();

    if (result.errCode === 0) {
      const stats = result.data;
      console.log(`服务名称填充率: ${stats.serviceNameFillRate}`);
      console.log(`服务价格填充率: ${stats.servicePriceFillRate}`);
    }
  } catch (error) {
    console.error('获取统计信息失败:', error);
  }
}

// 3. 执行修复操作
async function repairData(method = 'auto') {
  try {
    const response = await fetch(`/admin/data-consistency/repair?method=${method}`, {
      method: 'POST'
    });
    const result = await response.json();

    if (result.errCode === 0) {
      const { repairedCount, message } = result.data;
      console.log(`修复完成: ${message}`);
      return repairedCount;
    } else {
      throw new Error(result.msg);
    }
  } catch (error) {
    console.error('修复失败:', error);
    throw error;
  }
}

// 4. 补充订单原价
async function repairOriginalPrice(method = 'batch') {
  try {
    const response = await fetch(`/admin/data-consistency/repair-original-price?method=${method}`, {
      method: 'POST'
    });
    const result = await response.json();

    if (result.errCode === 0) {
      return result.data;
    } else {
      throw new Error(result.msg);
    }
  } catch (error) {
    console.error('补充原价失败:', error);
    throw error;
  }
}
```



## �📞 技术支持

如有接口使用问题，请联系后端开发团队或查看详细的技术文档。




